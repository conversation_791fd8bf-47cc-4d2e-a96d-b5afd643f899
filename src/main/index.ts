import {app, BrowserWindow, dialog, ipcMain, protocol, shell} from 'electron';
import path from 'path';
// import Store from 'electron-store';
import fs from 'fs';
import {v4 as uuidv4} from 'uuid';
import {execSync} from 'child_process';
import {getPythonProcessInfo, killPythonProcess, runPythonScript} from './service/PythonService';
import type {LogChannel, LogLevel} from './logger/core';
import loggerCore from './logger/core';
import logger from './logger/client';
import {getVoiceStore} from '@main/store/VoiceStore';
import { WindowManager } from '@main/service/WindowManager';
import { FinetuneService } from '@main/service/FinetuneService';
import {SpeechService} from '@main/service/SpeechService';
import type {TextToSpeechParams, TTSQueueItem, Voice, TTSProcess} from '@types';
import { TTSQueue } from '@main/service/TTSQueue';
import { TTSProcessStore } from '@main/store/TTSProcessStore';
import { getStoreValue, setStoreValue, createStore } from '@main/utils/store-helper';
import { pythonService } from '@main/service/PythonService';

// Khởi tạo electron-store để lưu cài đặt
const store = createStore({
  defaults: {
    theme: 'dark',
    language: 'en',
    speed: 1.0,
    defaultVoice: '',
    outputPath: app.getPath('downloads'),
    voicesPath: path.join(app.getPath('userData'), 'voices'),
    tempDir: path.join(app.getPath('userData'), 'temp')
  }
});

// Đảm bảo thư mục voices tồn tại
const voicesPath = getStoreValue(store, 'voicesPath') as string;
if (!fs.existsSync(voicesPath)) {
  fs.mkdirSync(voicesPath, {recursive: true});
}

const tempDir = getStoreValue(store, 'tempDir') as string;
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, {recursive: true});
}

// Khởi tạo VoiceStore
const voiceStore = getVoiceStore();

// Khởi tạo WindowManager
const windowManager = WindowManager.getInstance();

// Khởi tạo FinetuneService
const finetuneService = FinetuneService.getInstance();

// Khởi tạo SpeechService
const speechService = SpeechService.getInstance();

// Hàm lấy thời lượng audio sử dụng ffprobe
function getAudioDuration(filePath: string): number {
  try {
    // Sử dụng ffprobe để lấy thông tin về file audio
    const command = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${filePath}"`;
    const output = execSync(command).toString().trim();
    return parseFloat(output);
  } catch (error) {
    console.error(`Error getting audio duration for ${filePath}:`, error);
    return 0;
  }
}

let mainWindow: BrowserWindow | null = null;

// Tạo cửa sổ khi app sẵn sàng
app.whenReady().then(async () => {
  // await setupPython();
  // Đăng ký protocol local
  protocol.registerFileProtocol('local', (request, callback) => {
    const url = request.url.substr(7);
    try {
      return callback(decodeURIComponent(url));
    } catch (error) {
      console.error('ERROR: registerLocalProtocol: Could not get file path:', error);
    }
  });

  // Khởi tạo TTSQueue
  TTSQueue.getInstance();

  // Tạo cửa sổ chính
  windowManager.createWindow();

  // Ghi log khởi động ứng dụng
  logger.info('Gen Voice đã khởi động', {version: app.getVersion()}, 'system');

  app.on('activate', () => {
    windowManager.showWindow();
  });
});

// Đóng app khi tất cả cửa sổ đóng (trừ macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// IPC handlers
ipcMain.handle('getStoreValue', (_, key) => {
  return getStoreValue(store, key);
});

ipcMain.handle('setStoreValue', (_, key, value) => {
  setStoreValue(store, key, value);
  return true;
});

ipcMain.handle('selectFolder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory']
  });

  if (!result.canceled) {
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('selectVoiceZip', async () => {
  const result = await dialog.showOpenDialog({
    properties: ['openFile'],
    filters: [{name: 'ZIP Files', extensions: ['zip']}]
  });

  if (!result.canceled && result.filePaths.length > 0) {
    return result.filePaths[0];
  }
  return null;
});

ipcMain.handle('selectExportPath', async (_, defaultPath) => {
  const result = await dialog.showSaveDialog({
    defaultPath: defaultPath || path.join(app.getPath('downloads'), 'voice.zip'),
    filters: [{name: 'ZIP Files', extensions: ['zip']}]
  });

  if (!result.canceled && result.filePath) {
    return result.filePath;
  }
  return null;
});

ipcMain.handle('getAudioFiles', async (_, folderPath) => {
  try {
    const files = fs.readdirSync(folderPath);
    const audioFiles = files.filter(file =>
        file.endsWith('.wav') || file.endsWith('.mp3') ||
        file.endsWith('.flac') || file.endsWith('.ogg') ||
        file.endsWith('.m4a'));
    const result = [];

    for (const audioFile of audioFiles) {
      const extension = path.extname(audioFile);
      const baseName = path.basename(audioFile, extension);
      const textFilePath = path.join(folderPath, `${baseName}.txt`);
      const audioFilePath = path.join(folderPath, audioFile);

      if (fs.existsSync(textFilePath)) {
        const text = fs.readFileSync(textFilePath, 'utf-8');
        let duration = 0;
        try {
          duration = getAudioDuration(audioFilePath);
          duration = Math.round(duration * 10) / 10;
        } catch (err) {
          console.error(`Error getting audio duration for ${audioFile}:`, err);
          try {
            const stats = fs.statSync(audioFilePath);
            const fileSize = stats.size;
            duration = Math.round((fileSize / 88200) * 10) / 10;
          } catch (statErr) {
            console.error(`Fallback method also failed for ${audioFile}:`, statErr);
          }
        }

        result.push({
          audioPath: audioFilePath,
          textPath: textFilePath,
          text: text,
          fileName: baseName,
          duration: duration
        });
      }
    }

    return result;
  } catch (error) {
    console.error('Error reading audio files:', error);
    return [];
  }
});

// Voice operations
ipcMain.handle('getVoices', () => {
  return voiceStore.getVoices();
});

ipcMain.handle('getVoice', (_, id) => {
  return voiceStore.getVoice(id);
});

ipcMain.handle('addVoice', (_, voice) => {
  voiceStore.addVoice(voice);
  return true;
});

ipcMain.handle('updateVoice', async (_, voice: Voice) => {
  try {
    const updatedVoice = await voiceStore.updateVoice(voice);
    return updatedVoice;
  } catch (error) {
    console.error('Error updating voice:', error);
    throw error;
  }
});

ipcMain.handle('deleteVoice', async (_, voiceId: string) => {
  try {
    return await voiceStore.deleteVoice(voiceId);
  } catch (error) {
    console.error('Error deleting voice:', error);
    throw error;
  }
});

ipcMain.handle('setDefaultVoice', (_, id) => {
  voiceStore.setDefaultVoice(id);
  return true;
});

ipcMain.handle('getDefaultVoice', () => {
  return voiceStore.getDefaultVoice();
});

ipcMain.handle('importVoice', async (_, params: { importDir: string; voiceName?: string }) => {
  try {
    console.log('Debug - Main process received:', {
      importDir: params.importDir,
      voiceName: params.voiceName,
      type: typeof params.voiceName
    });
    const voiceStore = getVoiceStore();
    return await voiceStore.importVoice(params.importDir, params.voiceName);
  } catch (error) {
    console.error('Lỗi khi import voice:', error);
    return null;
  }
});

ipcMain.handle('exportVoice', async (_, id, outputPath) => {
  return await voiceStore.exportVoice(id, outputPath);
});

// TTS operations
ipcMain.handle('textToSpeech', async (_, options: TextToSpeechParams) => {
  try {
    const ttsQueue = TTSQueue.getInstance();
    const processId = options.processId || uuidv4();

    const task: TTSQueueItem = {
      processId,
      execute: async () => {
        const result = await speechService.textToSpeech({
          ...options,
          processId
        });
        if (result.error) {
          throw new Error(result.error);
        }
      },
      onError: (error: unknown) => {
        windowManager.sendToRenderer('processStatusUpdate', {
          processId,
          status: 'error',
          error: error instanceof Error ? error.message : String(error),
          updatedAt: new Date().toISOString()
        });
      }
    };

    await ttsQueue.addTask(task);
    return { processId, outputPath: options.outputPath };
  } catch (error) {
    console.error('Error occurred in handler for \'textToSpeech\':', error);
    return { error: error instanceof Error ? error.message : String(error) };
  }
});

// Metadata operations
ipcMain.handle('processMetadata', async (_, params) => {
  try {
    return await finetuneService.processMetadata({
      ...params,
      voicesPath: getStoreValue(store, 'voicesPath')
    });
  } catch (error) {
    console.error('Error processing metadata:', error);
    return {error: (error as Error).message};
  }
});

// Finetune operations
ipcMain.handle('startFinetune', async (_, {voiceName, trainMetadataPath, evalMetadataPath, epochs = 10, testText}) => {
  return finetuneService.startFinetune({
    voiceName,
    outputFolder: path.join(getStoreValue(store, 'voicesPath'), voiceName),
    trainMetadataPath,
    evalMetadataPath,
    epochs,
    testText
  });
});

ipcMain.handle('cancelFinetune', (_, processId) => {
  return killPythonProcess(processId);
});

ipcMain.handle('getFinetuneStatus', (_, processId) => {
  return getPythonProcessInfo(processId);
});

// Logger operations
ipcMain.handle('loggerLog', (_, level, message, data, channel) => {
  logger.log(level as LogLevel, message, data, channel as LogChannel);
  return true;
});

ipcMain.handle('getLogs', (_, filter) => {
  return loggerCore.getLogs(filter);
});

ipcMain.handle('clearLogs', () => {
  loggerCore.clearLogs();
  return true;
});

ipcMain.handle('getLogConfig', () => {
  return loggerCore.getConfig();
});

ipcMain.handle('updateLogConfig', (_, config) => {
  loggerCore.updateConfig(config);
  return true;
});

// Toast operations
ipcMain.handle('showToast', (_, {type, message}) => {
  windowManager.sendToRenderer('toast', {type, message});
  return true;
});

ipcMain.handle('updateProcess', async (_event, process) => {
  try {
    const store = TTSProcessStore.getInstance();
    store.updateProcess(process);
    return true;
  } catch (error) {
    console.error('Error updating process:', error);
    throw error;
  }
});

ipcMain.handle('removeProcess', async (_event, processId) => {
  try {
    const store = TTSProcessStore.getInstance();
    await store.deleteProcess(processId);
    return true;
  } catch (error) {
    console.error('Error removing process:', error);
    throw error;
  }
});

ipcMain.handle('getTTSProcesses', async () => {
  try {
    const store = TTSProcessStore.getInstance();
    return store.getProcesses();
  } catch (error) {
    console.error('Error getting TTS processes:', error);
    throw error;
  }
});

ipcMain.handle('cleanupCompletedProcesses', async () => {
  try {
    const store = TTSProcessStore.getInstance();
    const remainingProcesses = await store.cleanupCompletedProcesses();
    return remainingProcesses;
  } catch (error) {
    console.error('Error cleaning up completed processes:', error);
    throw error;
  }
});

ipcMain.handle('isExistFile', async (_event, filePath: string) => {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    console.error('Error checking file existence:', error);
    return false;
  }
});

ipcMain.handle('createProcess', async (_event, process: TTSProcess) => {
  try {
    const store = TTSProcessStore.getInstance();
    store.createProcess(process);
    return true;
  } catch (error) {
    console.error('Error creating process:', error);
    throw error;
  }
});

ipcMain.handle('cancelProcess', async (_event, processId) => {
  try {
    console.log('[cancelProcess] Gọi killPythonProcess với processId:', processId);
    const result = killPythonProcess(processId);
    if (!result) {
      console.warn('[cancelProcess] Không tìm thấy processId trong runningProcesses:', processId);
    }
    return result;
  } catch (error) {
    console.error('Error cancelling process:', error);
    return false;
  }
});

ipcMain.handle('selectFiles', async (_, options) => {
  try {
    const result = await dialog.showOpenDialog({
      filters: options.filters,
      properties: options.properties
    });
    return result.filePaths;
  } catch (error) {
    console.error('Error selecting files:', error);
    throw error;
  }
});

// Open userData folder
ipcMain.handle('openUserDataFolder', async () => {
  try {
    const userDataPath = app.getPath('userData');
    await shell.openPath(userDataPath);
    return true;
  } catch (error) {
    console.error('Error opening userData folder:', error);
    return false;
  }
});

ipcMain.handle('reloadChunk', async (_, params: {
  processId: string;
  chunkId: string;
  text: string;
  voice: Voice;
}) => {
  try {
    logger.info('IPC reloadChunk called', { processId: params.processId, chunkId: params.chunkId });

    // Sử dụng SpeechService để reload chunk
    const speechService = SpeechService.getInstance();
    const result = await speechService.reloadChunk(params);

    if (result.error) {
      logger.error('SpeechService reloadChunk error:', result.error);
      return { error: result.error };
    }

    // Gửi thông báo cập nhật process status về renderer
    const updatedProcess = TTSProcessStore.getInstance().getProcess(params.processId);
    if (updatedProcess) {
      windowManager.sendToRenderer('processStatusUpdate', {
        processId: params.processId,
        status: updatedProcess.status,
        progress: updatedProcess.progress,
        duration: updatedProcess.duration,
        executionTime: updatedProcess.executionTime,
        updatedAt: updatedProcess.updatedAt
      });
    }

    logger.info('Chunk reloaded successfully', { processId: params.processId, chunkId: params.chunkId });
    return { success: true };
  } catch (error) {
    logger.error('Error reloading chunk:', error);
    return { error: error instanceof Error ? error.message : String(error) };
  }
});

ipcMain.handle('getAudioDuration', async (_, audioPath: string) => {
  try {
    const duration = await pythonService.getAudioDuration(audioPath);
    return duration;
  } catch (error) {
    logger.error('Error getting audio duration:', error);
    throw error;
  }
});

ipcMain.handle('select-output-folder', async () => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory']
    });
    if (result.canceled) {
      return { error: 'Operation cancelled' };
    }
    return result.filePaths[0];
  } catch (error) {
    return { error: String(error) };
  }
});

ipcMain.handle('select-reference-files', async () => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openFile', 'multiSelections'],
      filters: [
        { name: 'Audio Files', extensions: ['wav', 'mp3', 'ogg', 'm4a'] }
      ]
    });
    if (result.canceled) {
      return { error: 'Operation cancelled' };
    }
    return result.filePaths;
  } catch (error) {
    return { error: String(error) };
  }
});

ipcMain.handle('preview-voice', async (_, voice: Voice) => {
  try {
    if (!voice.previewPath) {
      return { error: 'No preview file available for this voice' };
    }
    return voice.previewPath;
  } catch (error) {
    return { error: String(error) };
  }
});