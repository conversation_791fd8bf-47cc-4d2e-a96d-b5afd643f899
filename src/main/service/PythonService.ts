import {execSync, spawn} from 'child_process';
import type {SpawnOptions} from 'node:child_process';
import {app} from 'electron';
import path from 'path';
import fs from 'fs';
import logger from '../logger/client';
import type {MetadataMessage, Voice} from '@types';
import {ChildProcess} from 'child_process';

// Lưu trữ các tiến trình đang chạy
const runningProcesses: Record<string, {
  process: any,
  logs: string[],
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled',
  progress: number,
  onProgress?: (progress: number) => void,
  onLog?: (log: string) => void,
  onMessage?: (message: MetadataMessage) => void,
  onComplete?: (result: any) => void,
  onError?: (error: string) => void
}> = {};

// Xác định các đường dẫn
// Xác định môi trường
const isProduction = app.isPackaged;
const IS_WINDOWS = process.platform === 'win32';

// Đường dẫn tới thư mục bundled_python
const bundledPythonDir = isProduction
    ? path.join(process.resourcesPath, 'bundled_python')
    : path.join(app.getAppPath(), 'bundled_python');

// Đường dẫn tới môi trường ảo Python cho development
const devVenvDir = path.join(app.getAppPath(), 'venv');

// Đường dẫn tới thư mục Python scripts
const pythonScriptsDir = isProduction
    ? path.join(process.resourcesPath, 'src', 'python')
    : path.join(app.getAppPath(), 'src', 'python');

console.log('Python scripts directory:', pythonScriptsDir);

// Đường dẫn tới file check_dependencies.py
const checkDependenciesScript = path.join(pythonScriptsDir, 'check_dependencies.py');

interface PythonScriptOptions extends SpawnOptions {
  onProgress?: (progress: any) => void;
  onLog?: (log: string) => void;
  onMessage?: (message: any) => void;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

// Hàm tạo file .env với Python path
function createEnvFile(pythonPath: string): void {
  const envPath = path.join(app.getAppPath(), '.env');
  try {
    const envContent = `PYTHON_EXECUTABLE=${pythonPath}\n`;
    fs.writeFileSync(envPath, envContent, 'utf8');
    logger.debug(`Đã tạo file .env với Python path: ${pythonPath}`);
  } catch (error) {
    logger.error(`Không thể tạo file .env: ${error}`);
  }
}

// Sửa lại hàm getPythonExecutable để trả về file thực thi đã bundle
export function getPythonExecutable(scriptName?: string): string {
  // Nếu production, trả về file thực thi đã bundle
  if (isProduction) {
    // Trong production, luôn sử dụng main
    const execPath = path.join(bundledPythonDir, 'main', 'main');
    if (fs.existsSync(execPath)) {
      logger.debug(`Sử dụng file thực thi đã bundle: ${execPath}`);
      return execPath;
    }
    logger.error(`Không tìm thấy file thực thi đã bundle: ${execPath}`);
    return execPath;
  }
  // 1. Kiểm tra file .env trước
  const envPath = path.join(app.getAppPath(), '.env');
  let pythonExecutable: string | undefined;
  try {
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      pythonExecutable = envContent
        .split('\n')
        .find(line => line.startsWith('PYTHON_EXECUTABLE='))
        ?.split('=')[1]
        ?.trim();
      if (pythonExecutable && fs.existsSync(pythonExecutable)) {
        logger.debug(`Sử dụng Python từ .env: ${pythonExecutable}`);
        return pythonExecutable;
      }
    }
  } catch (error) {
    logger.warning(`Không thể đọc file .env: ${error}`);
  }
  // 2. Nếu đang ở môi trường development và có môi trường ảo development
  const devPythonPath = IS_WINDOWS
    ? path.join(devVenvDir, 'Scripts', 'python.exe')
    : path.join(devVenvDir, 'bin', 'python');
  if (!isProduction && fs.existsSync(devPythonPath)) {
    logger.debug(`Sử dụng Python từ môi trường ảo development: ${devPythonPath}`);
    createEnvFile(devPythonPath);
    return devPythonPath;
  }
  // 5. Cuối cùng, sử dụng Python hệ thống với các biến môi trường để tránh lỗi externally-managed-environment
  const systemPython = IS_WINDOWS ? 'python' : 'python3';
  logger.debug(`Sử dụng Python hệ thống: ${systemPython}`);
  logger.warning(`Cảnh báo: Nên chạy 'npm run setup' để cài đặt Python 3.10.17 trong môi trường ảo`);
  process.env.PYTHONNOUSERSITE = "1";
  process.env.PYTHONPATH = "";
  return systemPython;
}

// Hàm xử lý log từ Python process
function handlePythonLog(message: string, options: PythonScriptOptions) {
  try {
    // Parse JSON log từ Python
    const jsonData = JSON.parse(message);
    logger.log(jsonData.level, jsonData.message, jsonData.data, jsonData.channel, jsonData.type);

    if (jsonData.type === 'progress' && options.onProgress) {
      options.onProgress(jsonData.data);
    }

    if (jsonData.type === 'infor' && options.onLog) {
      options.onLog(jsonData.message);
    }

    if (jsonData.type === 'complete' && options.onComplete) {
      options.onComplete(jsonData.data);
    }

  } catch (error) {
    // Nếu không phải JSON, xử lý như log thông thường
    logger.info(message);
    if (options.onLog) {
      options.onLog(message);
    }
  }
}

// Hàm kiểm tra xem message có phải là lỗi không
function isErrorMessage(message: string): boolean {
  const errorKeywords = ['error', 'exception', 'traceback', 'failed', 'fatal'];
  return errorKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

// Sửa lại runPythonScript để log rõ ràng và chỉ truyền tên script khi production
export async function runPythonScript(
    scriptName: string, // chỉ truyền tên logic, không cần .py
    args: string[] = [],
    options: PythonScriptOptions = {},
    processId?: string,
): Promise<string> {
  const isProd = isProduction;
  const pythonPath = getPythonExecutable('main');
  if (!pythonPath) {
    throw new Error('Không tìm thấy file thực thi Python');
  }

  // Thêm service name và args vào finalArgs
  const finalArgs = isProd 
    ? [scriptName, ...args]  // Trong production, chỉ truyền service name và args
    : [path.join(pythonScriptsDir, 'main.py'), scriptName, ...args];  // Trong dev, thêm đường dẫn main.py

  // Log thông tin chi tiết về lệnh sẽ chạy
  logger.debug(`Running Python script: ${pythonPath} ${finalArgs.join(' ')}`);
  logger.debug(`Python script directory: ${pythonScriptsDir}`);
  logger.debug(`Working directory: ${process.cwd()}`);

  return new Promise((resolve, reject) => {
    const pythonProcess = spawn(pythonPath, finalArgs, {
      ...options,
      env: {
        ...process.env,
        PYTHONNOUSERSITE: '1',
        PYTHONPATH: isProd 
          ? path.join(bundledPythonDir, 'main', '_internal')  // Trong production, sử dụng _internal
          : `${pythonScriptsDir}${path.delimiter}${path.dirname(pythonScriptsDir)}${path.delimiter}${process.env.PYTHONPATH || ''}`,
        VIRTUAL_ENV: path.dirname(path.dirname(pythonPath)),
        PATH: `${path.dirname(pythonPath)}${path.delimiter}${process.env.PATH || ''}`
      },
      shell: false
    });

    // Lưu process vào runningProcesses nếu có processId
    if (processId) {
      runningProcesses[processId] = {
        process: pythonProcess,
        logs: [],
        status: 'processing',
        progress: 0,
        onProgress: options.onProgress,
        onLog: options.onLog,
        onMessage: options.onMessage,
        onComplete: options.onComplete,
        onError: options.onError
      };
    }

    let output = '';
    let error = '';

    // Xử lý output từ tiến trình Python (stdout)
    pythonProcess.stdout?.on('data', (data) => {
      const message = data.toString('utf8').trim();
      output += message + '\n';

      // Xử lý từng dòng riêng biệt
      const lines = message.split('\n');
      for (const line of lines) {
        if (!line.trim()) continue;
        handlePythonLog(line, options);
      }
    });

    // Xử lý output từ stderr
    pythonProcess.stderr?.on('data', (data) => {
      const message = data.toString('utf8').trim();
      output += message + '\n';

      // Kiểm tra xem có phải lỗi thực sự không
      if (isErrorMessage(message)) {
        logger.error(message);
        if (options.onError) {
          options.onError(message);
        }
      } else {
        handlePythonLog(message, options);
      }
    });

    pythonProcess.on('close', (code) => {
      if (code === 0) {
        logger.info('Process completed successfully', {processId}, 'system');
        resolve(output.trim());
      } else {
        const errorMessage = `Process exited with code ${code}\n${error}`;
        logger.error(errorMessage, {processId}, 'system');
        reject(new Error(errorMessage));
      }
    });

    pythonProcess.on('error', (err: Error) => {
      const errorMessage = `Failed to start Python process: ${err.message}`;
      logger.error(errorMessage, null, 'system');
      reject(new Error(errorMessage));
    });
  });
}

// Hàm hủy tiến trình Python (kill process tree)
export const killPythonProcess = (processId: string) => {
  if (runningProcesses[processId]) {
    const proc = runningProcesses[processId].process;
    const pid = proc.pid;
    console.log('[killPythonProcess] Đang kill processId:', processId, 'status:', runningProcesses[processId].status, 'pid:', pid);
    try {
      // Kill toàn bộ process tree
      if (process.platform === 'win32') {
        // Windows: Sử dụng taskkill để kill toàn bộ process tree
        execSync(`taskkill /PID ${pid} /T /F`, {stdio: 'ignore'});
      } else {
        // Unix-like (Mac/Linux): Kill process group và tất cả process con
        try {
          // 1. Tìm tất cả process con trước khi kill
          let childPids: string[] = [];
          try {
            // Sử dụng ps để lấy tất cả process con
            const output = execSync(`ps -ef | grep ${pid}`, {stdio: 'pipe'}).toString();
            const lines = output.split('\n').filter(line => line.trim());

            // Parse output để lấy PID
            childPids = lines
            .map(line => {
              const parts = line.trim().split(/\s+/);
              return parts[1]; // PID là cột thứ 2
            })
            .filter(pid => {
              const numPid = parseInt(pid);
              return !isNaN(numPid) && numPid !== process.pid;
            });

            if (childPids.length > 0) {
              console.log('[killPythonProcess] Tìm thấy các process con:', childPids);
            }
          } catch (e) {
            console.warn('[killPythonProcess] Không thể lấy danh sách process con:', e);
          }

          // 2. Kill từng process con
          childPids.forEach(childPid => {
            try {
              execSync(`kill -9 ${childPid}`, {stdio: 'ignore'});
              console.log(`[killPythonProcess] Đã kill process con ${childPid}`);
            } catch (e: any) {
              // Bỏ qua lỗi nếu process đã bị kill
              if (e.status === 1) {
                console.log(`[killPythonProcess] Process con ${childPid} đã bị kill trước đó`);
              } else {
                console.warn(`[killPythonProcess] Không thể kill process con ${childPid}:`, e);
              }
            }
          });

          // 3. Kill process chính
          try {
            execSync(`kill -9 ${pid}`, {stdio: 'ignore'});
            console.log(`[killPythonProcess] Đã kill process chính ${pid}`);
          } catch (e: any) {
            // Bỏ qua lỗi nếu process đã bị kill
            if (e.status === 1) {
              console.log(`[killPythonProcess] Process chính ${pid} đã bị kill trước đó`);
            } else {
              console.warn(`[killPythonProcess] Không thể kill process chính ${pid}:`, e);
            }
          }
        } catch (innerError) {
          console.warn('[killPythonProcess] Lỗi khi kill process con:', innerError);
        }
      }

      // Kill process chính cuối cùng
      try {
        proc.kill('SIGKILL');
        console.log('[killPythonProcess] Đã kill process chính bằng Node.js');
      } catch (e) {
        // Bỏ qua lỗi nếu process đã bị kill
        console.log('[killPythonProcess] Process chính đã bị kill trước đó');
      }

      // Đảm bảo process đã bị kill
      try {
        process.kill(pid, 0);
        console.warn('[killPythonProcess] Process vẫn còn tồn tại sau khi kill');
      } catch (e: any) {
        if (e.code === 'ESRCH') {
          console.log('[killPythonProcess] Đã kill process tree thành công');
        } else {
          console.warn('[killPythonProcess] Lỗi khi kiểm tra process:', e);
        }
      }
    } catch (e) {
      console.warn('[killPythonProcess] Lỗi khi kill process tree:', e);
    }
    // Cập nhật trạng thái thành cancelled thay vì failed
    runningProcesses[processId].status = 'cancelled';
    logger.warning(`Process ${processId} was cancelled by user`, {processId}, 'system');
    return true;
  }
  console.warn('[killPythonProcess] Không tìm thấy processId:', processId);
  return false;
};

// Hàm lấy thông tin tiến trình
export const getPythonProcessInfo = (processId: string) => {
  if (runningProcesses[processId]) {
    const {process, ...info} = runningProcesses[processId];
    return info;
  }
  return null;
};

// Thêm biến để track trạng thái setup
let isPythonSetup = false;
let isPythonSetupInProgress = false;

// Sửa lại hàm setupPython
// export const setupPython = async (): Promise<boolean> => {
//   // Nếu đã setup xong thì return true
//   if (isPythonSetup) {
//     return true;
//   }
//
//   // Nếu đang trong quá trình setup thì đợi
//   if (isPythonSetupInProgress) {
//     return new Promise((resolve) => {
//       const checkInterval = setInterval(() => {
//         if (isPythonSetup) {
//           clearInterval(checkInterval);
//           resolve(true);
//         }
//       }, 100);
//     });
//   }
//
//   isPythonSetupInProgress = true;
//
//   try {
//     // Trong development, bỏ qua kiểm tra dependencies
//     if (!isProduction) {
//       logger.info('Bỏ kiểm tra các phụ thuộc...', {}, 'system');
//       isPythonSetup = true;
//       isPythonSetupInProgress = false;
//       return true;
//     }
//
//     // Tìm file check_dependencies.py ở nhiều vị trí khác nhau
//     const possibleCheckDependenciesPaths = [
//       checkDependenciesScript,
//       path.join(process.resourcesPath, 'src', 'python', 'check_dependencies.py'),
//       path.join(app.getAppPath(), 'src', 'python', 'check_dependencies.py'),
//       path.join(process.resourcesPath, 'app.asar', 'src', 'python', 'check_dependencies.py'),
//       path.join(process.resourcesPath, 'app.asar.unpacked', 'src', 'python', 'check_dependencies.py')
//     ];
//
//     // Tìm file check_dependencies.py đầu tiên tồn tại
//     let checkDependenciesPath = null;
//     for (const possiblePath of possibleCheckDependenciesPaths) {
//       if (fs.existsSync(possiblePath)) {
//         checkDependenciesPath = possiblePath;
//         logger.debug(`Tìm thấy file check_dependencies.py tại: ${checkDependenciesPath}`);
//         break;
//       }
//     }
//
//     // Nếu không tìm thấy file check_dependencies.py, trả về false
//     if (!checkDependenciesPath) {
//       logger.error(`Không tìm thấy file check_dependencies.py trong các vị trí: ${possibleCheckDependenciesPaths.join(', ')}`);
//       return false;
//     }
//
//     return new Promise((resolve) => {
//       // Chạy script check_dependencies.py
//       runPythonScript(
//           checkDependenciesPath,
//           [],
//           {
//             onComplete: () => {
//               logger.debug('Kiểm tra phụ thuộc thành công!');
//               isPythonSetup = true;
//               isPythonSetupInProgress = false;
//               resolve(true);
//             },
//             onError: (error) => {
//               logger.error(`Kiểm tra phụ thuộc thất bại: ${error}`);
//               if (isProduction) {
//                 logger.warning('Tiếp tục trong môi trường production mặc dù kiểm tra phụ thuộc thất bại');
//                 isPythonSetup = true;
//                 isPythonSetupInProgress = false;
//                 resolve(true);
//               } else {
//                 isPythonSetupInProgress = false;
//                 resolve(false);
//               }
//             }
//           }
//       );
//     });
//   } catch (error) {
//     logger.error(`Lỗi khi thiết lập Python: ${error}`);
//     isPythonSetupInProgress = false;
//     if (isProduction) {
//       logger.warning('Tiếp tục trong môi trường production mặc dù thiết lập Python thất bại');
//       isPythonSetup = true;
//       return true;
//     }
//     return false;
//   }
// };
