import path from 'path';
import {WindowManager} from './WindowManager';
import {runPythonScript} from './PythonService';
import type {TextToSpeechParams, TTSChunk} from '@types';
import fs from 'fs';
import logger from '../logger/client';
import { getStoreValue, createStore } from '@main/utils/store-helper';
import {TTSProcessStore} from '../store/TTSProcessStore';

interface PythonScriptResult {
  error?: string;
  output?: string;
}

export class SpeechService {
  private static instance: SpeechService;
  private windowManager = WindowManager.getInstance();
  private store = createStore();
  private log = logger.channel('tts');
  private processStore: TTSProcessStore;

  private constructor() {
    this.processStore = TTSProcessStore.getInstance();
  }

  public static getInstance(): SpeechService {
    if (!SpeechService.instance) {
      SpeechService.instance = new SpeechService();
    }
    return SpeechService.instance;
  }

  /**
   * Chuyển đổi văn bản thành giọng nói
   * @param params Tham số đầu vào cho quá trình chuyển đổi
   * @returns Thông tin về quá trình xử lý và đường dẫn file output
   */
  public async textToSpeech(params: TextToSpeechParams) {
    try {
      this.log.info("Bắt đầu quá trình chuyển đổi văn bản thành giọng nói", {...params});
      const processId = params.processId

      // 1. Chuẩn bị các file và thư mục
      const {chunksDir, outputPath} = this.prepareOutputFiles(params);

      // 2. Tách văn bản thành các đoạn nhỏ
      const chunks = await this.splitTextIntoChunks(params.text, 'en', processId);

      // Cập nhật trạng thái running sau khi tách text thành công
      const process = this.processStore.getProcess(processId);
      if (process) {
        process.status = 'running';
        process.progress = 0;
        process.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(process);
      }

      this.windowManager.sendToRenderer('processStatusUpdate', {
        processId,
        status: 'running',
        progress: 0,
        updatedAt: new Date().toISOString(),

      });

      // 3. Xử lý từng đoạn văn bản
      const outputPaths = await this.processChunks(chunks, params, chunksDir, processId);

      // 4. Gộp các file âm thanh
      await this.mergeAudioFiles(outputPaths, outputPath);

      // 5. Dọn dẹp các file processor input/output
      const processorInputPath = path.join(chunksDir, 'processor_input.json');
      const processorOutputPath = path.join(chunksDir, 'processor_output.json');
      if (fs.existsSync(processorInputPath)) {
        fs.unlinkSync(processorInputPath);
      }
      if (fs.existsSync(processorOutputPath)) {
        fs.unlinkSync(processorOutputPath);
      }

      // Cập nhật trạng thái completed
      const latestProcess = this.processStore.getProcess(processId);
      if (latestProcess) {
        const executionTime = latestProcess.startTime ?
          new Date().getTime() - new Date(latestProcess.startTime).getTime() : undefined;

        // Đảm bảo duration đã được tính toán nếu chưa có
        if (!latestProcess.duration && latestProcess.chunks) {
          latestProcess.duration = this.calculateTotalDuration(latestProcess.chunks);
        }

        latestProcess.status = 'completed';
        latestProcess.progress = 100;
        latestProcess.audioPath = outputPath;
        latestProcess.executionTime = executionTime;
        latestProcess.updatedAt = new Date().toISOString();
        this.processStore.updateProcess(latestProcess);
      }

      this.windowManager.sendToRenderer('processStatusUpdate', {
        processId,
        status: 'completed',
        progress: 100,
        audioPath: outputPath,
        executionTime: latestProcess?.executionTime,
        duration: latestProcess?.duration,
        updatedAt: new Date().toISOString()
      });

      this.log.info("Đã hoàn thành quá trình chuyển đổi văn bản thành giọng nói", {processId, outputPath});
      return {processId, outputPath};
    } catch (error) {
      this.log.error('Error running text-to-speech:', error);
      return {error: String(error)};
    }
  }

  /**
   * Chuẩn bị các file và thư mục cần thiết cho quá trình xử lý
   * @param params Tham số đầu vào
   * @returns Thông tin về các đường dẫn file và thư mục
   */
  private prepareOutputFiles(params: TextToSpeechParams): { chunksDir: string; outputPath: string } {
    // Tạo thư mục gen_voice_chunks trong outputPath
    const totalChunksDir = path.join(params.outputPath, 'gen_voice_chunks');
    fs.mkdirSync(totalChunksDir, {recursive: true});

    // Tạo thư mục con cho process theo fileName
    const chunksDir = path.join(totalChunksDir, params.fileName);
    fs.mkdirSync(chunksDir, {recursive: true});

    // Đường dẫn file output audio (lưu trực tiếp trong outputPath)
    const outputPath = path.join(params.outputPath, `${params.fileName}.wav`);

    return {chunksDir, outputPath};
  }

  /**
   * Tách văn bản thành các đoạn nhỏ để xử lý
   * @param text Văn bản cần tách
   * @param language Ngôn ngữ của văn bản
   * @param processId ID của tiến trình
   * @returns Mảng các đoạn văn bản đã tách
   */
  public async splitTextIntoChunks(text: string, language: string, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu tách text", {text: text.slice(0, 50) + "...", language});
    const outputPathJson = path.join(getStoreValue(this.store, 'tempDir') as string, `tts_${processId}`, 'chunks.json');
    fs.mkdirSync(path.dirname(outputPathJson), {recursive: true});
    fs.writeFileSync(outputPathJson, '[]', 'utf-8');

    // Cập nhật trạng thái processing và gán startTime
    const process = this.processStore.getProcess(processId);
    if (process) {
      process.status = 'processing';
      process.startTime = new Date().toISOString(); // Ghi lại thời điểm bắt đầu
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);
    }

    // Gửi trạng thái processing
    this.windowManager.sendToRenderer('processStatusUpdate', {
      processId,
      status: 'processing',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    // Thay thế toàn bộ ký tự " trong text thành rỗng
    const safeText = text.replace(/"/g, '');

    const splitResult = await runPythonScript('text_utils', [
      '--text', JSON.stringify(safeText),
      '--output', outputPathJson,
      '--language', language,
      '--process_id', processId
    ]) as PythonScriptResult;

    if (splitResult.error) {
      this.log.error("Lỗi khi tách text", {error: splitResult.error});
      throw new Error(splitResult.error);
    }

    const jsonContent = fs.readFileSync(outputPathJson, 'utf-8');
    const chunks = JSON.parse(jsonContent);

    if (!chunks || chunks.length === 0) {
      this.log.error("Không thể tách text thành các đoạn");
      throw new Error('Không thể tách text thành các đoạn');
    }

    this.log.debug("Đã tách text thành công", {numChunks: chunks.length});
    return chunks;
  }

  /**
   * Xử lý các đoạn văn bản thành file âm thanh
   * @param chunks Các đoạn văn bản cần xử lý
   * @param params Tham số đầu vào
   * @param chunksDir Thư mục lưu trữ chunks
   * @param processId
   * @returns Mảng đường dẫn các file âm thanh đã tạo
   */
  private async processChunks(chunks: string[], params: TextToSpeechParams, chunksDir: string, processId: string): Promise<string[]> {
    this.log.debug("Bắt đầu xử lý các đoạn", {numChunks: chunks.length});
    const chunksOutputs = chunks.map((_: any, index: number) =>
        path.join(chunksDir, `chunk_${index + 1}.wav`)
    );

    // Tạo thông tin chunks cho process
    const process = this.processStore.getProcess(processId);
    if (process) {
      process.chunks = chunks.map((text, index) => ({
        id: `${processId}_chunk_${index + 1}`,
        text,
        status: 'pending',
        audioPath: chunksOutputs[index]
      }));
      this.processStore.updateProcess(process);
    }

    const argsList = chunks.map((chunk: string, index: number) => {
      return this.buildTTSArgs({
        ...params,
        text: chunk,
        outputPath: chunksOutputs[index],
        processId
      });
    });

    const processorInput = {
      chunks,
      num_workers: params.numWorkers || 1,
      args_list: argsList
    };

    const processorInputPath = path.join(chunksDir, 'processor_input.json');
    fs.writeFileSync(processorInputPath, JSON.stringify(processorInput));

    // Gửi trạng thái running với progress 0
    this.windowManager.sendToRenderer('processStatusUpdate', {
      processId,
      status: 'running',
      progress: 0,
      updatedAt: new Date().toISOString()
    });

    const processorResult = await runPythonScript('tts_processor', [
      processorInputPath,
      '--process_id', processId
    ], {
      onProgress: (data) => {
        // Cập nhật trạng thái với progress từ Python
        if (process && process.chunks && data.chunk_info) {
          // Tìm chunk theo ID từ chunk_info
          const chunkIndex = process.chunks.findIndex(c => c.id === data.chunk_info.id);
          if (chunkIndex !== -1) {
            const chunk = process.chunks[chunkIndex];
            chunk.status = data.chunk_info.status;
            if (data.chunk_info.audioPath) {
              chunk.audioPath = data.chunk_info.audioPath;
            }
            if (data.chunk_info.duration) {
              chunk.duration = data.chunk_info.duration;
            }
            if (data.chunk_info.error) {
              chunk.error = data.chunk_info.error;
            }
            this.processStore.updateProcess(process);

            // Gửi cập nhật chunk đến renderer
            this.windowManager.sendToRenderer('processStatusUpdate', {
              processId,
              status: 'running',
              progress: data.progress || 0,
              chunkInfo: {
                id: chunk.id,
                status: data.chunk_info.status,
                audioPath: data.chunk_info.audioPath,
                duration: data.chunk_info.duration
              },
              updatedAt: new Date().toISOString()
            });
          }
        }

        // Kiểm tra nếu tất cả chunk đã hoàn thành
        if (data.all_chunks_completed && process && process.chunks) {
          const allCompleted = process.chunks.every(chunk => chunk.status === 'completed');
          if (allCompleted) {
            // Tính tổng duration từ các chunk
            const totalDuration = this.calculateTotalDuration(process.chunks);
            process.duration = totalDuration;
            this.processStore.updateProcess(process);

            this.log.info("Tất cả chunk đã hoàn thành, chuẩn bị merge audio", {
              processId,
              totalChunks: process.chunks.length,
              totalDuration
            });

            // Gửi thông báo tất cả chunk đã hoàn thành với duration
            this.windowManager.sendToRenderer('processStatusUpdate', {
              processId,
              status: 'running',
              progress: 100,
              allChunksCompleted: true,
              duration: totalDuration,
              updatedAt: new Date().toISOString()
            });
          }
        }
      },
      onLog: (log) => {
        try {
          const data = JSON.parse(log);
          if (data && data.status === 'completed' && data.output_path && process && process.chunks && data.chunk_index !== undefined) {
            const chunk = process.chunks[data.chunk_index];
            if (chunk) {
              chunk.status = 'completed';
              chunk.audioPath = data.output_path;
              chunk.duration = data.duration;
              this.processStore.updateProcess(process);

              // Cập nhật trạng thái cho renderer
              this.windowManager.sendToRenderer('processStatusUpdate', {
                processId,
                status: 'running',
                progress: data.progress || 100,
                chunkInfo: {
                  id: chunk.id,
                  status: 'completed',
                  audioPath: data.output_path,
                  duration: data.duration
                },
                updatedAt: new Date().toISOString()
              });
            }
            this.log.debug("Chunk completed", {
              processId,
              outputPath: data.output_path
            });
          }
        } catch {
        }
      }
    }, processId) as PythonScriptResult;

    if (processorResult.error) {
      // Cập nhật trạng thái lỗi cho tất cả chunks
      if (process && process.chunks) {
        process.chunks.forEach(chunk => {
          chunk.status = 'error';
          chunk.error = processorResult.error;
        });
        this.processStore.updateProcess(process);
      }
      throw new Error(processorResult.error);
    }

    // Đọc kết quả từ file tạm
    const outputFile = path.join(chunksDir, 'processor_output.json');
    if (!fs.existsSync(outputFile)) {
      throw new Error('Không tìm thấy file kết quả từ processor');
    }

    try {
      const outputContent = fs.readFileSync(outputFile, 'utf-8');
      const results = JSON.parse(outputContent) as { output_paths?: string[] };

      if (!results.output_paths) {
        throw new Error('Không nhận được kết quả từ processor');
      }

      this.log.debug("Đã xử lý các đoạn thành công", {numOutputs: results.output_paths.length});
      return results.output_paths;
    } catch (error) {
      this.log.error("Lỗi khi đọc kết quả từ processor", {error, outputFile});
      throw error;
    }
  }

  /**
   * Tạo danh sách tham số cho lệnh chuyển văn bản thành giọng nói
   * @param params Tham số đầu vào
   * @returns Mảng các tham số dòng lệnh
   */
  private buildTTSArgs(params: TextToSpeechParams): string[] {
    const args = [
      '--text', params.text,
      '--output', params.outputPath,
      '--model_path', params.voice.modelPath,
      '--language', params.language || 'en',
      '--speed', (params.voice.speed ?? 1.0).toString(),
      '--temperature', (params.voice.temperature ?? 0.7).toString(),
      '--repetition_penalty', (params.voice.repetitionPenalty ?? 2.0).toString(),
      '--top_k', (params.voice.topK ?? 50).toString(),
      '--top_p', (params.voice.topP ?? 0.85).toString(),
      '--length_penalty', (params.voice.lengthPenalty ?? 1.0).toString(),
      '--num_threads', (params.numThreads ?? 1).toString(),
      '--process_id', params.processId || ''
    ];

    // Thêm speaker_wav nếu có referencePath
    if (params.voice.referencePath && params.voice.referencePath.length > 0) {
      // Kiểm tra và chuẩn hóa đường dẫn file
      const validPaths = params.voice.referencePath.filter(p => {
        const exists = fs.existsSync(p);
        if (!exists) {
          this.log.warning(`File not found: ${p}`);
        }
        return exists;
      });

      if (validPaths.length === 0) {
        throw new Error('No valid reference audio files found');
      }

      // Thêm từng file một cách riêng biệt
      validPaths.forEach(p => {
        args.push('--speaker_wav', p);
      });
    } else {
      throw new Error('Voice reference path is required');
    }

    // Thêm các tham số tùy chọn
    if (params.voice.configPath) {
      args.push('--config_path', params.voice.configPath);
    }
    if (params.voice.vocabPath) {
      args.push('--vocab_path', params.voice.vocabPath);
    }

    this.log.debug("Đã tạo args cho TTS", {args});
    return args;
  }

  /**
   * Tính tổng duration từ các chunk
   * @param chunks Mảng các chunk cần tính duration
   * @returns Tổng duration tính bằng giây
   */
  private calculateTotalDuration(chunks: TTSChunk[]): number {
    return chunks.reduce((total, chunk) => {
      return total + (chunk.duration || 0);
    }, 0);
  }

  /**
   * Gộp các file âm thanh thành một file duy nhất
   * @param outputPaths Mảng đường dẫn các file âm thanh cần gộp
   * @param outputPath Đường dẫn file output
   */
  private async mergeAudioFiles(outputPaths: string[], outputPath: string): Promise<void> {
    this.log.debug("Bắt đầu gộp các file âm thanh", {numFiles: outputPaths.length, outputPath});
    const mergeResult = await runPythonScript(
        'audio_utils',
        [
          '--input', JSON.stringify(outputPaths),
          '--output', outputPath,
          '--sample_rate', '24000',
          '--gap_duration', '0.1'
        ]
    ) as PythonScriptResult;

    if (mergeResult.error) {
      this.log.error("Lỗi khi gộp file âm thanh", {error: mergeResult.error});
      throw new Error(mergeResult.error);
    }

    this.log.debug("Đã gộp các file âm thanh thành công");
  }

  /**
   * Lấy thời lượng của file âm thanh
   * @param audioPath Đường dẫn đến file âm thanh
   * @returns Thời lượng tính bằng giây
   */
  public async getAudioDuration(audioPath: string): Promise<number> {
    try {
      const result = await runPythonScript('audio_utils', [
        '--get_duration',
        '--audio_path', audioPath
      ]) as PythonScriptResult;

      if (result.error) {
        this.log.error('Lỗi khi lấy duration của file audio', { audioPath, error: result.error });
        return 0;
      }

      // Parse duration từ stdout
      const duration = parseFloat(result.stdout || '0');
      return isNaN(duration) ? 0 : duration;
    } catch (error) {
      this.log.error('Lỗi khi lấy duration của file audio', { audioPath, error });
      return 0;
    }
  }

  /**
   * Reload một chunk và tự động gộp lại với các chunks khác
   * @returns Kết quả của quá trình reload
   * @param params
   */
  public async reloadChunk(params: { processId: string; chunkId: string; text: string }) {
    const reloadStartTime = Date.now();
    try {
      const {processId, chunkId, text} = params;
      this.log.info("Bắt đầu reload chunk", {processId, chunkId});

      // Lấy process từ store
      const process = this.processStore.getProcess(processId);
      if (!process) {
        throw new Error('Process not found');
      }

      // Tìm chunk cần reload
      const chunkIndex = process.chunks?.findIndex(c => c.id === chunkId);
      if (chunkIndex === -1 || !process.chunks) {
        throw new Error('Chunk not found');
      }

      // Cập nhật text của chunk
      process.chunks[chunkIndex].text = text;
      process.chunks[chunkIndex].status = 'running';
      this.processStore.updateProcess(process);

      // Gọi trực tiếp tts.py để xử lý chunk độc lập (không qua queue)
      const chunkOutputPath = process.chunks[chunkIndex].audioPath;
      if (!chunkOutputPath) {
        throw new Error('Chunk audio path not found');
      }

      // Xây dựng args cho tts.py
      const args = [
        '--text', text,
        '--output', chunkOutputPath,
        '--model_path', process.voice.modelPath,
        '--config_path', process.voice.configPath,
        '--vocab_path', process.voice.vocabPath,
        '--speaker_wav', JSON.stringify(process.voice.referencePath),
        '--language', 'en',
        '--speed', process.voice.speed?.toString() || '1.0',
        '--temperature', process.voice.temperature?.toString() || '0.5',
        '--repetition_penalty', process.voice.repetitionPenalty?.toString() || '1.0',
        '--top_k', process.voice.topK?.toString() || '25',
        '--top_p', process.voice.topP?.toString() || '0.65',
        '--length_penalty', process.voice.lengthPenalty?.toString() || '1.0',
        '--process_id', processId,
        '--chunk_index', chunkIndex.toString()
      ];

      // Chạy tts.py trực tiếp
      const ttsResult = await runPythonScript('tts', args, {}, processId) as PythonScriptResult;

      if (ttsResult.error) {
        throw new Error(ttsResult.error);
      }

      // Cập nhật trạng thái chunk thành completed và tính duration
      const duration = await this.getAudioDuration(chunkOutputPath);
      process.chunks[chunkIndex].status = 'completed';
      process.chunks[chunkIndex].duration = duration;
      this.processStore.updateProcess(process);

      // Gộp lại tất cả các chunks thành file audio chính
      const allChunks = process.chunks
        .filter(c => c.status === 'completed' && c.audioPath)
        .map(c => c.audioPath!);

      if (allChunks.length === 0) {
        throw new Error('No completed chunks found for merging');
      }

      const outputPath = process.audioPath || path.join(getStoreValue(this.store, 'outputPath') as string, `${processId}.wav`);
      await this.mergeAudioFiles(allChunks, outputPath);

      // Tính lại duration tổng và execution time
      const reloadTime = Date.now() - reloadStartTime;
      const newTotalDuration = this.calculateTotalDuration(process.chunks);
      const currentExecutionTime = process.executionTime || 0;

      // Cập nhật trạng thái process
      process.audioPath = outputPath;
      process.duration = newTotalDuration;
      process.executionTime = currentExecutionTime + reloadTime;
      process.updatedAt = new Date().toISOString();
      this.processStore.updateProcess(process);

      this.log.info("Đã reload chunk thành công", {
        processId,
        chunkId,
        reloadTime: `${reloadTime}ms`,
        newDuration: `${newTotalDuration}s`,
        totalExecutionTime: `${process.executionTime}ms`
      });

      return {success: true};
    } catch (error) {
      this.log.error('Error reloading chunk:', error);

      // Cập nhật trạng thái chunk thành failed nếu có lỗi
      const process = this.processStore.getProcess(params.processId);
      if (process && process.chunks) {
        const chunkIndex = process.chunks.findIndex(c => c.id === params.chunkId);
        if (chunkIndex !== -1) {
          process.chunks[chunkIndex].status = 'failed';
          process.chunks[chunkIndex].error = String(error);
          this.processStore.updateProcess(process);
        }
      }

      return {error: String(error)};
    }
  }

}