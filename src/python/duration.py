#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module xử lý tính toán thời lượng của file âm thanh.
"""

import argparse
import os
import sys
from typing import Optional
from logger import get_logger
import torch
import torchaudio

# Khởi tạo logger
logger = get_logger("duration", "general")


def get_audio_duration(audio_file: str) -> float:
  """
  Lấy thời lượng của file âm thanh.

  Args:
    audio_file: Đường dẫn đến file âm thanh

  Returns:
    float: Thời lượng của file âm thanh tính bằng giây
  """
  try:
    if not os.path.exists(audio_file):
      logger.warning(f"File âm thanh không tồn tại: {audio_file}")
      return 0.0

    # Đọc file âm thanh
    waveform, sample_rate = torchaudio.load(audio_file)

    # Tính thời lượng (số sample / sample rate)
    duration = waveform.shape[-1] / sample_rate

    logger.debug(f"Duration của {audio_file}: {duration:.2f}s")
    return float(duration)

  except Exception as e:
    logger.error(f"Lỗi khi tính duration của file {audio_file}: {str(e)}")
    return 0.0


def main():
  """
  Main function để xử lý command line arguments và tính duration.
  """
  parser = argparse.ArgumentParser(description='Audio duration calculator')
  parser.add_argument('--audio_path', type=str, required=True,
                      help='Path to audio file for duration calculation')
  parser.add_argument('--process_id', type=str, help='Process ID for logging')

  args = parser.parse_args()

  # Set process_id cho logger nếu có
  if args.process_id:
    logger.set_process_id(args.process_id)

  try:
    if not args.audio_path:
      logger.error("--audio_path là bắt buộc")
      sys.exit(1)

    duration = get_audio_duration(args.audio_path)

    # Sử dụng log.data với type complete để trả về duration
    logger.data("Tính duration thành công", {
      "type": "complete",
      "duration": duration,
      "audio_path": args.audio_path
    })

    logger.debug(f"Đã tính duration thành công: {duration}s",
                 {"process_id": args.process_id, "audio_path": args.audio_path})

  except Exception as e:
    logger.error(f"Lỗi khi tính duration: {str(e)}", {"process_id": args.process_id})
    sys.exit(1)


if __name__ == '__main__':
  main()