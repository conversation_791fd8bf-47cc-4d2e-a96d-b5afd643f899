#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Module khởi động chính cho ứng dụng Python.
Xử lý tất cả các service thông qua command line arguments.
"""

import multiprocessing
import os
import sys
import argparse
from typing import Dict, Any, Optional, List

# Thêm thư mục hiện tại vào PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from logger import get_logger # Đảm bảo logger.py có thể được import
import text_utils
import tts_processor
import metadata_processor
import finetune
import check_dependencies
import audio_utils
import tts

if __name__ == '__main__':
    multiprocessing.freeze_support()

logger = get_logger("main", "system")

def is_production() -> bool:
    return getattr(sys, 'frozen', False)

def setup_environment():
    pass # Hi<PERSON><PERSON> t<PERSON><PERSON> không cần

def get_service_module(service_name: str) -> Optional[Any]:
    service_map: Dict[str, Any] = {
        'text_utils': text_utils,
        'tts_processor': tts_processor,
        'metadata_processor': metadata_processor,
        'finetune': finetune,
        'check_dependencies': check_dependencies,
        'audio_utils': audio_utils,
        'tts': tts
    }
    module = service_map.get(service_name)
    if not module:
        logger.error(f"Không tìm thấy service: {service_name}")
    return module

def run_service(service_name: str, service_specific_args: List[str]) -> int:
    module = get_service_module(service_name)
    if not module:
        return 1
    
    try:
        if hasattr(module, 'main'):
            original_argv = list(sys.argv)
            # Script name cho module con có thể là tên service hoặc một placeholder
            sys.argv = [f'{service_name}.py'] + service_specific_args 
            
            logger.info(f"Đang chạy service: {service_name} với args: {service_specific_args}")
            logger.debug(f"sys.argv cho module con được đặt thành: {sys.argv}")

            try:
                exit_code = module.main() 
                return exit_code if isinstance(exit_code, int) else 0
            finally:
                sys.argv = original_argv
                logger.debug(f"Đã khôi phục sys.argv gốc: {sys.argv}")
        else:
            logger.error(f"Service {service_name} không có hàm main() để thực thi.")
            return 1
    except Exception as e:
        # Bỏ exc_info=True nếu logger của bạn không hỗ trợ
        logger.error(f"Lỗi nghiêm trọng khi chạy service {service_name}: {str(e)}") 
        # Nếu muốn xem traceback đầy đủ trên console khi gỡ lỗi, bạn có thể thêm:
        # import traceback
        # logger.error(traceback.format_exc())
        return 1

def entry_point() -> int:
    try:
        setup_environment()
        
        # Kiểm tra xem có đủ argument không (ít nhất là tên chương trình và tên service)
        if len(sys.argv) < 2:
            # In thông báo hướng dẫn đơn giản nếu không có service_name
            # Bạn có thể tạo một ArgumentParser ở đây chỉ để in --help nếu muốn
            sys.stderr.write("Lỗi: Cần chỉ định một 'service' để chạy.\n")
            sys.stderr.write(f"Cách dùng: {os.path.basename(sys.argv[0])} <tên_service> [các_tham_số_cho_service...]\n")
            return 1

        service_name = sys.argv[1]
        # Tất cả các argument còn lại sau service_name được coi là args cho service đó
        service_args = sys.argv[2:]
            
        logger.info(f"Yêu cầu chạy service: '{service_name}' với args: {service_args}")
        
        return run_service(service_name, service_args)
        
    except SystemExit as e:
        logger.info(f"SystemExit được gọi, mã thoát: {e.code}")
        return e.code if isinstance(e.code, int) else 0
    except Exception as e:
        # Bỏ exc_info=True nếu logger của bạn không hỗ trợ
        logger.error(f"Lỗi không mong muốn trong entry_point: {str(e)}")
        # import traceback
        # logger.error(traceback.format_exc()) # Để xem traceback đầy đủ
        return 1

if __name__ == "__main__":
    exit_code = entry_point()
    logger.info(f"Tiến trình Python (service: {sys.argv[1] if len(sys.argv) > 1 else 'N/A'}) kết thúc với mã: {exit_code}")
    sys.exit(exit_code)