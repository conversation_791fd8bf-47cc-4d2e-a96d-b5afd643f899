#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Module xử lý các thao tác với file âm thanh.
"""

import argparse
import json
from typing import List, Optional
from logger import get_logger
import torch
import torchaudio
import os

# Khởi tạo logger
logger = get_logger("audio_utils", "general")


def get_audio_duration(audio_file: str) -> float:
  """
  Lấy thời lượng của file âm thanh.

  Args:
    audio_file: Đường dẫn đến file âm thanh

  Returns:
    float: Thời lượng của file âm thanh tính bằng giây
  """
  try:
    if not os.path.exists(audio_file):
      logger.warning(f"File âm thanh không tồn tại: {audio_file}")
      return 0.0

    # Đọc file âm thanh
    waveform, sample_rate = torchaudio.load(audio_file)

    # Tính thời lượng (số sample / sample rate)
    duration = waveform.shape[-1] / sample_rate

    logger.debug(f"Duration của {audio_file}: {duration:.2f}s")
    return float(duration)

  except Exception as e:
    logger.error(f"Lỗi khi tính duration của file {audio_file}: {str(e)}")
    return 0.0


def merge_audio_files(input_files: List[str], output_file: str) -> None:
  """
  Gộp nhiều file âm thanh thành một file duy nhất.

  Args:
    input_files: Danh sách đường dẫn đến các file âm thanh đầu vào
    output_file: Đường dẫn đến file âm thanh đầu ra
  """
  try:
    # Đọc tất cả các file âm thanh
    audio_segments = []
    for file in input_files:
      waveform, sample_rate = torchaudio.load(file)
      audio_segments.append(waveform)

    # Gộp các đoạn âm thanh
    merged_audio = torch.cat(audio_segments, dim=-1)

    # Lưu file âm thanh đã gộp
    torchaudio.save(output_file, merged_audio, sample_rate)

  except Exception as e:
    logger.error(f"Lỗi khi gộp file âm thanh: {str(e)}")
    raise e


def main():
  parser = argparse.ArgumentParser(description='Audio utilities')
  parser.add_argument('--input', help='JSON string of input file paths')
  parser.add_argument('--output', help='Output audio file path')
  parser.add_argument('--sample_rate', type=int, default=24000,
                      help='Sample rate of output audio')
  parser.add_argument('--gap_duration', type=float, default=0.2,
                      help='Gap duration between audio files in seconds')
  parser.add_argument('--process_id', type=str, help='Process ID for logging')
  parser.add_argument('--get_duration', action='store_true',
                      help='Get duration of audio file')
  parser.add_argument('--audio_path', type=str,
                      help='Path to audio file for duration calculation')

  args = parser.parse_args()

  # Set process_id cho logger nếu có
  if args.process_id:
    logger.set_process_id(args.process_id)

  try:
    if args.get_duration:
      if not args.audio_path:
        logger.error("--audio_path là bắt buộc khi sử dụng --get_duration")
        exit(1)

      duration = get_audio_duration(args.audio_path)
      print(duration)  # In ra stdout để SpeechService có thể đọc

    else:
      # Merge audio files
      if not args.input or not args.output:
        logger.error("--input và --output là bắt buộc khi merge audio")
        exit(1)

      input_files = json.loads(args.input)
      logger.info("Bắt đầu gộp các file âm thanh",
                  {"process_id": args.process_id, "num_files": len(input_files)})

      merge_audio_files(input_files, args.output)

      logger.info("Đã gộp các file âm thanh thành công",
                  {"process_id": args.process_id, "output": args.output})

  except Exception as e:
    logger.error(f"Lỗi: {str(e)}", {"process_id": args.process_id})
    exit(1)


if __name__ == '__main__':
  main()
